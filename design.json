{"designSystem": {"name": "Modern Task Management UI", "version": "1.0", "theme": "dark", "colorPalette": {"background": {"primary": "#1a1a1a", "secondary": "#2a2a2a", "tertiary": "#363636"}, "surface": {"card": "#2d2d2d", "elevated": "#3a3a3a", "overlay": "#404040"}, "accent": {"purple": "#8b5cf6", "blue": "#3b82f6", "green": "#10b981", "yellow": "#f59e0b", "pink": "#ec4899", "orange": "#f97316"}, "text": {"primary": "#ffffff", "secondary": "#a3a3a3", "muted": "#737373", "inverse": "#000000"}, "status": {"success": "#22c55e", "warning": "#eab308", "error": "#ef4444", "info": "#3b82f6"}}, "typography": {"fontFamily": {"primary": "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "mono": "'SF Mono', 'Monaco', 'Inconsolata', monospace"}, "fontSize": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem"}, "fontWeight": {"normal": 400, "medium": 500, "semibold": 600, "bold": 700}, "lineHeight": {"tight": 1.25, "normal": 1.5, "relaxed": 1.75}}, "spacing": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem", "2xl": "3rem", "3xl": "4rem"}, "borderRadius": {"sm": "0.375rem", "md": "0.5rem", "lg": "0.75rem", "xl": "1rem", "full": "9999px"}, "shadows": {"sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)"}, "components": {"card": {"base": {"backgroundColor": "surface.card", "borderRadius": "borderRadius.lg", "padding": "spacing.lg", "boxShadow": "shadows.md", "border": "1px solid rgba(255, 255, 255, 0.1)"}, "variants": {"elevated": {"backgroundColor": "surface.elevated", "boxShadow": "shadows.lg"}, "outlined": {"backgroundColor": "transparent", "border": "1px solid rgba(255, 255, 255, 0.2)"}}}, "button": {"base": {"borderRadius": "borderRadius.full", "padding": "spacing.sm spacing.lg", "fontSize": "fontSize.sm", "fontWeight": "fontWeight.medium", "transition": "all 0.2s ease", "cursor": "pointer"}, "variants": {"primary": {"backgroundColor": "accent.purple", "color": "text.inverse", "hover": {"backgroundColor": "#7c3aed"}}, "secondary": {"backgroundColor": "surface.elevated", "color": "text.primary", "border": "1px solid rgba(255, 255, 255, 0.2)"}, "ghost": {"backgroundColor": "transparent", "color": "text.secondary", "hover": {"backgroundColor": "surface.card"}}}}, "tag": {"base": {"borderRadius": "borderRadius.full", "padding": "spacing.xs spacing.sm", "fontSize": "fontSize.xs", "fontWeight": "fontWeight.medium", "display": "inline-flex", "alignItems": "center"}, "variants": {"purple": {"backgroundColor": "rgba(139, 92, 246, 0.2)", "color": "accent.purple"}, "green": {"backgroundColor": "rgba(16, 185, 129, 0.2)", "color": "accent.green"}, "yellow": {"backgroundColor": "rgba(245, 158, 11, 0.2)", "color": "accent.yellow"}, "blue": {"backgroundColor": "rgba(59, 130, 246, 0.2)", "color": "accent.blue"}}}, "avatar": {"base": {"borderRadius": "borderRadius.full", "border": "2px solid rgba(255, 255, 255, 0.1)", "overflow": "hidden"}, "sizes": {"sm": "1.5rem", "md": "2rem", "lg": "2.5rem", "xl": "3rem"}}, "progressBar": {"base": {"height": "0.5rem", "backgroundColor": "surface.elevated", "borderRadius": "borderRadius.full", "overflow": "hidden"}, "fill": {"height": "100%", "borderRadius": "borderRadius.full", "transition": "width 0.3s ease"}}, "input": {"base": {"backgroundColor": "surface.card", "border": "1px solid rgba(255, 255, 255, 0.1)", "borderRadius": "borderRadius.md", "padding": "spacing.sm spacing.md", "fontSize": "fontSize.sm", "color": "text.primary", "placeholder": {"color": "text.muted"}, "focus": {"outline": "none", "borderColor": "accent.purple", "boxShadow": "0 0 0 2px rgba(139, 92, 246, 0.2)"}}}}, "layouts": {"sidebar": {"width": "280px", "backgroundColor": "background.secondary", "padding": "spacing.lg", "borderRight": "1px solid rgba(255, 255, 255, 0.1)"}, "mainContent": {"backgroundColor": "background.primary", "padding": "spacing.xl", "minHeight": "100vh"}, "grid": {"gap": "spacing.lg", "responsive": {"mobile": "1fr", "tablet": "repeat(2, 1fr)", "desktop": "repeat(3, 1fr)"}}}, "patterns": {"taskCard": {"structure": {"header": {"title": "fontSize.base, fontWeight.semibold", "subtitle": "fontSize.sm, color.text.secondary", "actions": "position: absolute, top-right"}, "content": {"description": "fontSize.sm, color.text.secondary, lineHeight.relaxed", "tags": "display: flex, gap: spacing.xs, flexWrap: wrap"}, "footer": {"avatars": "display: flex, overlap: -spacing.xs", "metadata": "display: flex, alignItems: center, gap: spacing.sm", "progress": "marginTop: spacing.md"}}}, "dashboardMetric": {"structure": {"value": "fontSize.3xl, fontWeight.bold, color.text.primary", "label": "fontSize.sm, color.text.secondary", "change": "fontSize.xs, fontWeight.medium", "visualization": "marginTop: spacing.md"}}, "projectList": {"structure": {"item": {"icon": "size: 2rem, borderRadius: md", "content": "flex: 1, marginLeft: spacing.md", "progress": "width: 120px, marginLeft: auto"}}}}, "animations": {"transitions": {"fast": "0.15s ease", "normal": "0.2s ease", "slow": "0.3s ease"}, "effects": {"fadeIn": "opacity 0 to 1", "slideUp": "transform translateY(10px) to translateY(0)", "scaleIn": "transform scale(0.95) to scale(1)", "hover": "transform translateY(-2px), boxShadow enhanced"}}, "iconography": {"style": "outline", "strokeWidth": "1.5px", "sizes": {"sm": "1rem", "md": "1.25rem", "lg": "1.5rem", "xl": "2rem"}}, "dataVisualization": {"charts": {"colors": ["accent.purple", "accent.blue", "accent.green", "accent.yellow", "accent.pink"], "backgroundColor": "surface.card", "gridLines": "rgba(255, 255, 255, 0.1)", "labels": "text.secondary"}, "progressRings": {"strokeWidth": "8px", "backgroundColor": "surface.elevated", "foregroundColor": "accent.green"}}, "responsiveBreakpoints": {"mobile": "320px", "tablet": "768px", "desktop": "1024px", "wide": "1280px"}, "accessibility": {"focusOutline": "2px solid accent.purple", "minTouchTarget": "44px", "contrastRatio": "WCAG AA compliant", "reducedMotion": "respect prefers-reduced-motion"}}}